import '../../db/db.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../models/utils.dart';
import '../../../logs/app_logger.dart';

class GetUtils {
  SupabaseClient supabase = Supabase.instance.client;
  final DB db = DB();

  Future<Utils> getProduto() async {
    try {
      final data = await supabase.from(db.tabelaDeUtils).select().single();
      final Utils utils = Utils.fromMap(data);
      AppLogger.logInfo('Tags recuperados com sucesso: $utils');
      return utils;
    } catch (e, stackTrace) {
      AppLogger.logError('Erro ao buscar tags', e, stackTrace);
      rethrow;
    }
  }
}
