import 'package:flutter/material.dart';

class ScrollService {
  static final ScrollService _instance = ScrollService._internal();
  factory ScrollService() => _instance;
  ScrollService._internal();

  final Map<String, ScrollController> _scrollControllers = {};
  final Map<String, double> _scrollPositions = {};

  /// Registra um ScrollController para uma página específica
  void registerScrollController(
    String pageKey,
    ScrollController controller,
  ) {
    if (!_scrollControllers.containsKey(pageKey)) {
      _scrollControllers[pageKey] = controller;
    }
  }

  /// Remove o registro do ScrollController de uma página específica
  void unregisterScrollController(String pageKey) {
    _scrollControllers[pageKey]?.dispose();
    _scrollControllers.remove(pageKey);
    _scrollPositions.remove(pageKey);
  }

  /// Salva a posição atual da rolagem para uma página específica
  void saveScrollPosition(String pageKey) {
    final controller = _scrollControllers[pageKey];
    if (controller?.hasClients ?? false) {
      _scrollPositions[pageKey] = controller!.offset;
    }
  }

  /// Restaura a posição salva da rolagem para uma página específica
  void restoreScrollPosition(String pageKey) {
    final controller = _scrollControllers[pageKey];
    if (controller?.hasClients ?? false) {
      final position = _scrollPositions[pageKey] ?? 0.0;
      controller!.jumpTo(position);
    }
  }

  /// Rola a página para o topo com animação personalizada
  Future<void> scrollToTop(
    String pageKey, {
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeOut,
  }) async {
    final controller = _scrollControllers[pageKey];
    if (controller?.hasClients ?? false) {
      await controller!.animateTo(
        0.0,
        duration: duration,
        curve: curve,
      );
    }
  }

  /// Verifica se existe um ScrollController registrado para a página
  bool hasScrollController(String pageKey) {
    return _scrollControllers.containsKey(pageKey);
  }

  /// Retorna a posição atual da rolagem para uma página específica
  double? getScrollPosition(String pageKey) {
    return _scrollPositions[pageKey];
  }

  /// Retorna o ScrollController para uma página específica
  ScrollController? getScrollController(String pageKey) {
    return _scrollControllers[pageKey];
  }

  /// Limpa todos os controladores e posições salvos
  void dispose() {
    _scrollControllers.clear();
    _scrollPositions.clear();
  }
}
